{"name": "my-portfolio", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@react-three/drei": "^9.92.5", "@react-three/fiber": "^8.15.12", "framer-motion": "^10.16.16", "maath": "^0.10.7", "react": "^18.2.0", "react-color": "^2.19.3", "react-dom": "^18.2.0", "react-icons": "^4.12.0", "react-router-dom": "^6.21.1", "react-tilt": "^1.0.2", "react-vertical-timeline-component": "^3.6.0", "three": "^0.159.0", "valtio": "^1.12.1"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.16", "eslint": "^8.55.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.32", "tailwindcss": "^3.4.0", "vite": "^5.0.8"}}