import {
  mobile,
  backend,
  creator,
  web,
  javascript,
  typescript,
  html,
  css,
  reactjs,
  redux,
  tailwind,
  nodejs,
  mongodb,
  git,
  figma,
  docker,
  meta,
  starbucks,
  tesla,
  shopify,
  carrent,
  jobit,
  tripguide,
  threejs,
  php,
  mysql,
  java,
  adminportal,
  customersite,
  javaselenium,
  selenium,
  aws,
  wowtruck,
} from "../assets";

export const navLinks = [
  {
    id: "about",
    title: "About",
  },
  {
    id: "work",
    title: "Work",
  },
  {
    id: "contact",
    title: "Contact",
  },
];

const services = [
  {
    title: "Web Developer",
    icon: web,
  },
  // {
  //   title: "React Developer",
  //   icon: mobile,
  // },
  {
    title: "Backend Developer",
    icon: backend,
  },
  {
    title: "DevOps Developer",
    icon: creator,
  },
];

const technologies = [
  {
    name: "HTML 5",
    icon: html,
  },
  {
    name: "CSS",
    icon: css,
  },
  {
    name: "JavaScript",
    icon: javascript,
  },
  // {
  //   name: "TypeScript",
  //   icon: typescript,
  // },

  {
    name: "PHP",
    icon: php,
  },
  {
    name: "MySQL",
    icon: mysql,
  },

  {
    name: "React JS",
    icon: reactjs,
  },
  {
    name: "Core Java",
    icon: java,
  },
  {
    name: "Redux Toolkit",
    icon: redux,
  },
  {
    name: "Tailwind CSS",
    icon: tailwind,
  },
  {
    name: "Node JS",
    icon: nodejs,
  },
  {
    name: "MongoDB",
    icon: mongodb,
  },
  // {
  //   name: "Three JS",
  //   icon: threejs,
  // },
  {
    name: "selenium",
    icon: selenium,
  },
  {
    name: "AWS",
    icon: aws,
  },
  {
    name: "git",
    icon: git,
  },
  // {
  //   name: "figma",
  //   icon: figma,
  // },
  // {
  //   name: "docker",
  //   icon: docker,
  // },
];

const experiences = [
  {
    title: "Core Java Developer",
    company_name: "WowTruck",
    icon: wowtruck,
    iconBg: "#E6DEDD",
    date: "Feb 2022 - May 2022",
    points: [
      "Implemented a robust Automation solution using JAVA and Selenium for Amazon Relay, aimed at streamlining and automating manual processes, resulting in a significant reduction in human workload.",
      "Leveraged Selenium's capabilities to enhance testing efficiency and ensure system reliability.",
      "Collaborated with cross-functional teams to identify automation opportunities and implement solutions, contributing to increased operational efficiency within the organization.",
    ],
  },
  {
    title: "Web Developer",
    company_name: "WowTruck",
    icon: wowtruck,
    iconBg: "#E6DEDD",
    date: "Jun 2022 - Now",
    points: [
      "Developing and maintaining web applications using PHP(Yii) framework and other related technologies.",
      "Collaborating with cross-functional teams including designers, product managers, and other developers to create high-quality products.",
      "Implementing responsive design and ensuring cross-browser compatibility.",
      "Participating in code reviews and providing constructive feedback to other developers.",
    ],
  },
  {
    title: "React.js Developer",
    company_name: "WowTruck",
    icon: wowtruck,
    iconBg: "#E6DEDD",
    date: "Dec 2022 - Now",
    points: [
      "Developing and maintaining web applications using React.js and other related technologies.",
      "Collaborating with cross-functional teams including designers, product managers, and other developers to create high-quality products.",
      "Implementing responsive design and ensuring cross-browser compatibility.",
      "Participating in code reviews and providing constructive feedback to other developers.",
    ],
  },
  // {
  //   title: "Full stack Developer",
  //   company_name: "Meta",
  //   icon: meta,
  //   iconBg: "#E6DEDD",
  //   date: "Jan 2023 - Present",
  //   points: [
  //     "Developing and maintaining web applications using React.js and other related technologies.",
  //     "Collaborating with cross-functional teams including designers, product managers, and other developers to create high-quality products.",
  //     "Implementing responsive design and ensuring cross-browser compatibility.",
  //     "Participating in code reviews and providing constructive feedback to other developers.",
  //   ],
  // },
];

const testimonials = [
  {
    testimonial:
      "I thought it was impossible to make a website as beautiful as our product, but Rick proved me wrong.",
    name: "Sara Lee",
    designation: "CFO",
    company: "Acme Co",
    image: "https://randomuser.me/api/portraits/women/4.jpg",
  },
  {
    testimonial:
      "I've never met a web developer who truly cares about their clients' success like Rick does.",
    name: "Chris Brown",
    designation: "COO",
    company: "DEF Corp",
    image: "https://randomuser.me/api/portraits/men/5.jpg",
  },
  {
    testimonial:
      "After Rick optimized our website, our traffic increased by 50%. We can't thank them enough!",
    name: "Lisa Wang",
    designation: "CTO",
    company: "456 Enterprises",
    image: "https://randomuser.me/api/portraits/women/6.jpg",
  },
];

const projects = [
  {
    name: "Logistics Booking Platform",
    description:
      "Created a user-friendly truck booking platform, enabling efficient service bookings. Established secure user authentication, safeguarding customer data and building trust. Integrated real-time tracking for visibility into truck locations. Rigorously tested for a reliable, error-free user experience.",
    tags: [
      {
        name: "React.js",
        color: "blue-text-gradient",
      },
      {
        name: "PHP,RestAPI,MySQL",
        color: "green-text-gradient",
      },
      {
        name: "Redux",
        color: "pink-text-gradient",
      },
    ],
    image: customersite,
    source_code_link: "https://wowtruck.in/",
  },
  {
    name: "Wowtruck Admin Portal",
    description:
      "Innovative logistics platform designed for efficient last-mile connectivity, enhancing the customer experience, and optimizing earnings for truck drivers.The project encompasses essential modules such as bookings, transporter, customer, payment & finance, user management, and reports. The admin portal seamlessly oversees the driver app, customer app, and customer portal for effective management.",
    tags: [
      {
        name: "PHP-YII2",
        color: "blue-text-gradient",
      },
      {
        name: "RestApi,MySQL",
        color: "green-text-gradient",
      },
      {
        name: "CSS, Bootstrap",
        color: "pink-text-gradient",
      },
    ],
    image: adminportal,
    source_code_link: "",
  },
  {
    name: "Amazon Relay Automation",
    description:
      "Implemented a robust Automation solution using JAVA and Selenium for Amazon Relay, aimed at streamlining and automating manual processes, resulting in a significant reduction in human workload.Leveraged Selenium's capabilities to enhance testing efficiency and ensure system reliability. Collaborated with cross-functional teams to identify automation opportunities and implement solutions, contributing to increased operational efficiency within the organization.",
    tags: [
      {
        name: "Java,Selenium",
        color: "blue-text-gradient",
      },
      {
        name: "PHP,MySQL",
        color: "green-text-gradient",
      },
      {
        name: "RestAPI,Jenkins",
        color: "pink-text-gradient",
      },
    ],
    image: javaselenium,
    source_code_link: "",
  },
];

// const LocationIcon = () => (
//   <svg
//     className="mk-svg-icon"
//     data-name="mk-moon-location-3"
//     data-cacheid="icon-6583cebc6071e"
//     style={{ height: "48px", width: "48px" }}
//     xmlns="http://www.w3.org/2000/svg"
//     viewBox="0 0 512 512"
//   >
//     <path
//       d="M256 480c-88.366 0-160-71.634-160-160 0-160 160-352 160-352s160 192 160 352c0 88.366-71.635 160-160 160zm0-258c-54.124 0-98 43.876-98 98s43.876 98 98 98 98-43.876 98-98-43.876-98-98-98zm-62 98a62 62 1260 1 0 124 0 62 62 1260 1 0-124 0z"
//       transform="scale(1 -1) translate(0 -480)"
//     ></path>
//   </svg>
// );

// const PhoneIcon = () => (
//   <svg
//     className="mk-svg-icon"
//     data-name="mk-moon-phone-4"
//     data-cacheid="icon-6583cebc60b6e"
//     style={{ height: "48px", width: "48px" }}
//     xmlns="http://www.w3.org/2000/svg"
//     viewBox="0 0 512 512"
//   >
//     <path d="M321.788 371.146c-11.188 6.236-20.175 2.064-32.764-4.572..."></path>
//   </svg>
// );

// const BubbleDotsIcon = () => (
//   <svg
//     className="mk-svg-icon"
//     data-name="mk-moon-bubble-dots"
//     data-cacheid="icon-6583cebc60f82"
//     style={{ height: "48px", width: "48px" }}
//     xmlns="http://www.w3.org/2000/svg"
//     viewBox="0 0 512 512"
//   >
//     <path d="M418 32h-324c-51.7 0-94 42.3-94 94v354l96-96h322c51.7 0 94-42.3 94-94v-164c0-51.7-42.3-94-94-94zm-258 224c-17.673 0-32-14.327-32-32s14.327-32 32-32 32 14.327 32 32-14.327 32-32 32zm96 0c-17.673 0-32-14.327-32-32s14.327-32 32-32 32 14.327 32 32-14.327 32-32 32zm96 0c-17.673 0-32-14.327-32-32s14.327-32 32-32 32 14.327 32 32-14.327 32-32 32z"></path>
//   </svg>
// );
const contact = [
  {
    name: "ADDRESS",
    logo: 1,
    text: "Chennai",
  },
  {
    logo: 2,
    name: "PHONE",
    text: "+91 8682902484",
  },
  {
    logo: 3,
    name: "EMAIL",
    text: "<EMAIL>",
    // text: "<EMAIL>",
  },
];
export { services, technologies, experiences, testimonials, projects, contact };
