import logo from "./logo_200x200.png";
import backend from "./backend.png";
import creator from "./creator.png";
import mobile from "./mobile.png";
import web from "./web.png";
import github from "./github.png";
import menu from "./menu.svg";
import close from "./close.svg";

import css from "./tech/css.png";
import docker from "./tech/docker.png";
import figma from "./tech/figma.png";
import git from "./tech/git.png";
import html from "./tech/html.png";
import javascript from "./tech/javascript.png";
import mongodb from "./tech/mongodb.png";
import nodejs from "./tech/nodejs.png";
import reactjs from "./tech/reactjs.png";
import redux from "./tech/redux.png";
import tailwind from "./tech/tailwind.png";
import typescript from "./tech/typescript.png";
import threejs from "./tech/threejs.svg";
import php from "./tech/php.png";
import mysql from "./tech/mysql.png";
import java from "./tech/java.png";
import selenium from "./tech/se.png";
import aws from "./tech/aws.jpg";

import meta from "./company/meta.png";
import shopify from "./company/shopify.png";
import starbucks from "./company/starbucks.png";
import tesla from "./company/tesla.png";
import wowtruck from "./company/wowtruck-logo-bgr.png";
import carrent from "./carrent.png";
import jobit from "./jobit.png";
import tripguide from "./tripguide.png";

import customersite from "./customersite.jpg";
import adminportal from "./adminportal.png";
import javaselenium from "./java_selenium.png";

export {
  logo,
  backend,
  creator,
  mobile,
  web,
  github,
  menu,
  close,
  css,
  docker,
  figma,
  git,
  html,
  javascript,
  mongodb,
  nodejs,
  reactjs,
  redux,
  tailwind,
  typescript,
  threejs,
  meta,
  shopify,
  starbucks,
  tesla,
  carrent,
  jobit,
  tripguide,
  php,
  mysql,
  java,
  adminportal,
  customersite,
  javaselenium,
  selenium,
  aws,
  wowtruck,
};
